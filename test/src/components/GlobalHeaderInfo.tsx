import React, { useEffect, useState } from 'react';

const GlobalHeaderInfo: React.FC = () => {
  const [time, setTime] = useState<string>(new Date().toLocaleString());

  useEffect(() => {
    const timer = setInterval(() => {
      setTime(new Date().toLocaleString());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  return (
    <div style={{ marginBottom: 16, fontWeight: 500 }}>
      当前时间：{time}
    </div>
  );
};

export default GlobalHeaderInfo;